import {
  Alignment,
  Fit,
  Layout,
  useRive,
  useStateMachineInput,
} from "@rive-app/react-canvas";

export const CarRiveComponent = () => {
  // Possible Clean state -> Clean_

  const { RiveComponent, rive } = useRive({
    src: "clean_the_car.riv",
    stateMachines: "Motion",
    layout: new Layout({
      fit: Fit.FitWidth,
      alignment: Alignment.Center,
    }),
    autoplay: true,
  });

  const carScreenInput = useStateMachineInput(rive, "Motion", "Clean_10");

  const handleCarUpdate = () => {
    carScreenInput.fire();
  };

  return (
    <>
      <RiveComponent />
      <button
        className="bg-teal-700 text-white px-6 py-3 mt-4 mx-4 rounded-md"
        onClick={handleCarUpdate}
      >
        Update Car Screen
      </button>
    </>
  );
};

const App = () => {
  return (
    <div className="h-[80vh] w-3/4 bg-black">
      <CarRiveComponent />
    </div>
  );
};
export default App;
